const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a category name'],
      unique: true,
      trim: true,
      maxlength: [50, 'Name cannot be more than 50 characters']
    },
    description: {
      type: String,
      maxlength: [500, 'Description cannot be more than 500 characters']
    }
  },
  { timestamps: true }
);

// Create index for faster queries
CategorySchema.index({ name: 1 });

module.exports = mongoose.model('Category', CategorySchema); 