const User = require('../models/User');
const { ErrorResponse } = require('../middleware/errorHandler');
const crypto = require('crypto');

// @desc    Register a user
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  try {
    const { username, email, password, role } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ username }, { email }] });
    if (existingUser) {
      return next(new ErrorResponse('User already exists', 400));
    }
    
    // Create new user
    const user = await User.create({
      username,
      email,
      password,
      role: role || 'user'
    });
    
    // 记录首次登录
    await user.recordLogin();
    
    // Send token
    sendTokenResponse(user, 201, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;
    
    // Check if user exists
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return next(new ErrorResponse('Invalid credentials', 401));
    }
    
    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      return next(new ErrorResponse('Invalid credentials', 401));
    }
    
    // 记录用户登录活动
    await user.recordLogin();
    
    // Send token
    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);
    
    // 更新用户活动时间
    await user.updateActivity();
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
exports.updateProfile = async (req, res, next) => {
  try {
    // 只允许更新特定字段
    const allowedUpdates = {
      'profile.name': req.body.name,
      'profile.bio': req.body.bio,
      'profile.location': req.body.location,
      'profile.website': req.body.website,
      'profile.social.twitter': req.body.twitter,
      'profile.social.facebook': req.body.facebook,
      'profile.social.linkedin': req.body.linkedin,
      'profile.social.github': req.body.github,
      'preferences.emailNotifications': req.body.emailNotifications,
      'preferences.theme': req.body.theme,
      'preferences.articlesPerPage': req.body.articlesPerPage
    };
    
    // 过滤掉未定义的值
    const updates = {};
    Object.entries(allowedUpdates).forEach(([key, value]) => {
      if (value !== undefined) {
        updates[key] = value;
      }
    });
    
    // 更新用户
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updates },
      { new: true, runValidators: true }
    );
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update password
// @route   PUT /api/auth/password
// @access  Private
exports.updatePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    // 检查当前密码
    const user = await User.findById(req.user.id).select('+password');
    
    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }
    
    // 验证当前密码
    const isMatch = await user.matchPassword(currentPassword);
    if (!isMatch) {
      return next(new ErrorResponse('Current password is incorrect', 401));
    }
    
    // 更新密码
    user.password = newPassword;
    await user.save();
    
    // 发送新令牌
    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;
    
    const user = await User.findOne({ email });
    
    if (!user) {
      return next(new ErrorResponse('User not found with that email', 404));
    }
    
    // 生成重置令牌
    const resetToken = crypto.randomBytes(20).toString('hex');
    
    // 创建哈希令牌并设置过期时间
    user.resetPasswordToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');
      
    // 设置过期时间 (10分钟)
    user.resetPasswordExpire = Date.now() + 10 * 60 * 1000;
    
    await user.save({ validateBeforeSave: false });
    
    // 在实际应用中，这里会发送重置链接邮件
    // 为了测试，我们直接返回令牌
    res.status(200).json({
      success: true,
      resetToken
    });
  } catch (err) {
    next(err);
  }
};

// Helper function to get token from model, create cookie and send response
const sendTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();
  
  res.status(statusCode).json({
    success: true,
    token,
    user: {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      preferences: user.preferences
    }
  });
};