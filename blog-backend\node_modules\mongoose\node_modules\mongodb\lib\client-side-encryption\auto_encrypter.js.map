{"version": 3, "file": "auto_encrypter.js", "sourceRoot": "", "sources": ["../../src/client-side-encryption/auto_encrypter.ts"], "names": [], "mappings": ";;;;AAKA,2BAA2B;AAE3B,kCAAgE;AAEhE,4CAA+C;AAC/C,kCAAqD;AACrD,oCAA6C;AAC7C,kDAAuE;AAEvE,oCAAsD;AACtD,2DAA8D;AAC9D,sDAAsD;AACtD,qCAA0D;AAC1D,+DAA2D;AAC3D,2CAKqB;AACrB,mDAAwE;AAsGxE,cAAc;AACD,QAAA,yBAAyB,GAAG,MAAM,CAAC,MAAM,CAAC;IACrD,UAAU,EAAE,CAAC;IACb,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACA,CAAC,CAAC;AAiBZ;;;GAGG;AACH,MAAa,aAAa;IA6BxB,gBAAgB;IAChB,MAAM,CAAC,aAAa;QAClB,MAAM,UAAU,GAAG,IAAA,iCAA0B,GAAE,CAAC;QAChD,IAAI,cAAc,IAAI,UAAU,EAAE,CAAC;YACjC,MAAM,UAAU,CAAC,YAAY,CAAC;QAChC,CAAC;QACD,OAAO,UAAU,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACH,YAAY,MAAmB,EAAE,OAA8B;QAnE/D;;;;;;;WAOG;QACH,QAAiB,GAAG,KAAK,CAAC;QA4DxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,KAAK,IAAI,CAAC;QAE/D,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,IAAI,gBAAgB,CAAC;QACxE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC;QACxD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC;QACxD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QAExD,IAAI,OAAO,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,IAAA,8BAAkB,EAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACvF,MAAM,IAAI,uCAA8B,CACtC,8HAA8H,CAC/H,CAAC;QACJ,CAAC;QAED,MAAM,iBAAiB,GAAsB;YAC3C,sBAAsB,EAAE,IAAI;YAC5B,eAAe;SAChB,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,iBAAiB,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;gBAC9D,CAAC,CAAC,OAAO,CAAC,SAAS;gBACnB,CAAC,CAAE,IAAA,gBAAS,EAAC,OAAO,CAAC,SAAS,CAAY,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,iBAAiB,CAAC,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC;gBAChF,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAC5B,CAAC,CAAE,IAAA,gBAAS,EAAC,OAAO,CAAC,kBAAkB,CAAY,CAAC;QACxD,CAAC;QAED,iBAAiB,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YACnE,CAAC,CAAE,IAAA,gBAAS,EAAC,IAAI,CAAC,aAAa,CAAY;YAC3C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAEvB,IAAI,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YAC5B,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACpE,iBAAiB,CAAC,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC;QACjF,CAAC;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,iBAAiB,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QACtE,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;YACpC,iBAAiB,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAEhG,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC;YAC3E,0BAA0B;YAC1B,iBAAiB,CAAC,yBAAyB,GAAG,OAAO,CAAC,YAAY,CAAC,yBAAyB,CAAC;QAC/F,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAClD,iBAAiB,CAAC,yBAAyB,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IACE,OAAO,CAAC,YAAY;YACpB,OAAO,CAAC,YAAY,CAAC,sBAAsB;YAC3C,CAAC,IAAI,CAAC,yBAAyB,EAC/B,CAAC;YACD,MAAM,IAAI,uCAA8B,CACtC,iEAAiE,CAClE,CAAC;QACJ,CAAC;QAED,oEAAoE;QACpE,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC9E,IAAI,CAAC,mBAAmB,GAAG,IAAI,wCAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACxE,MAAM,aAAa,GAAuB;gBACxC,wBAAwB,EAAE,KAAK;aAChC,CAAC;YAEF,IACE,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,YAAY,CAAC,cAAc,KAAK,QAAQ,CAAC;gBACzF,CAAC,GAAG,CAAC,0BAA0B,EAC/B,CAAC;gBACD,iEAAiE;gBACjE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3B,CAAC;YAED,6DAA6D;YAC7D,wFAAwF;YACxF,IAAI,GAAG,CAAC,0BAA0B,EAAE,CAAC;gBACnC,0FAA0F;gBAC1F,6DAA6D;gBAC7D,kFAAkF;gBAClF,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAA,2CAAuB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;YACvF,CAAC;YAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,0BAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC5E,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,MAAM,IAAI,yBAAiB,CACzB,sHAAsH,CACvH,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,IAAI,yBAAiB,CACzB,qHAAqH,CACtH,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACvD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,yBAAiB,CACzB,mGAAmG,EACnG,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,EAAU,EACV,GAAa,EACb,UAAsC,EAAE;QAExC,OAAO,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,8DAA8D;YAC9D,OAAO,GAAG,CAAC;QACb,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,gBAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE3E,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CACpD,kCAA0B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,EAC5C,aAAa,CACd,CAAC;QAEF,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACpC,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;QAChB,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEvB,MAAM,YAAY,GAAG,IAAI,4BAAY,CAAC;YACpC,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,aAAa,EAAE,IAAA,2CAAuB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;SAC/D,CAAC,CAAC;QAEH,OAAO,IAAA,kBAAW,EAAC,MAAM,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;YACrE,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,QAAoB,EACpB,UAAsC,EAAE;QAExC,OAAO,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEjE,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEpC,MAAM,YAAY,GAAG,IAAI,4BAAY,CAAC;YACpC,GAAG,OAAO;YACV,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,aAAa,EAAE,IAAA,2CAAuB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;SAC/D,CAAC,CAAC;QAEH,OAAO,MAAM,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO,MAAM,IAAA,iCAAqB,EAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACpF,CAAC;IAED;;;;OAIG;IACH,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC;IACpD,CAAC;IAED,MAAM,KAAK,oBAAoB;QAC7B,OAAO,aAAa,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC;IAC5D,CAAC;CACF;AAhUD,sCAgUC;KArSE,2BAAe"}