module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  
  // 覆盖率收集
  collectCoverageFrom: [
    'controllers/**/*.js',
    'middleware/**/*.js',
    'models/**/*.js',
    'routes/**/*.js',
    'utils/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  
  // 覆盖率报告格式
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 覆盖率输出目录
  coverageDirectory: 'coverage',
  
  // 测试超时时间 (60秒)
  testTimeout: 60000,
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/jest.setup.js'],
  
  // 清除模拟
  clearMocks: true,
  
  // 详细输出
  verbose: true,

  // 单线程运行避免数据库冲突
  maxWorkers: 1,

  // 强制退出
  forceExit: true,

  // 检测未关闭的句柄
  detectOpenHandles: true
};
