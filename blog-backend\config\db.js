const mongoose = require('mongoose');
const config = require('./config');

/**
 * 连接到MongoDB数据库
 * 优化配置以支持MongoDB 4.2.25特性
 */
const connectDB = async () => {
  try {
    // MongoDB 4.2.25兼容选项
    const conn = await mongoose.connect(config.mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // 启用MongoDB 4.2聚合管道支持
      useFindAndModify: false,
      // 启用MongoDB事务支持
      autoIndex: true,
      // 连接池配置
      poolSize: 10,
      // 读写关注级别
      readConcern: { level: 'local' },
      writeConcern: { w: 'majority' }
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    
    // 配置MongoDB会话支持
    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB connection disconnected');
    });

    mongoose.connection.on('error', (err) => {
      console.error(`MongoDB connection error: ${err.message}`);
    });

    // 启用MongoDB调试模式（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      mongoose.set('debug', true);
    }

    return conn;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);
    process.exit(1);
  }
};

module.exports = connectDB; 