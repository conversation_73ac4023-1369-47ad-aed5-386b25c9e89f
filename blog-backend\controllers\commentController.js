const Comment = require('../models/Comment');
const Article = require('../models/Article');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Get comments for article
// @route   GET /api/articles/:articleId/comments
// @access  Public
exports.getComments = async (req, res, next) => {
  try {
    const article = await Article.findById(req.params.articleId);
    
    if (!article) {
      return next(new ErrorResponse(`No article found with id ${req.params.articleId}`, 404));
    }
    
    const comments = await Comment.find({ article: req.params.articleId })
      .sort({ createdAt: -1 })
      .populate({
        path: 'author',
        select: 'username'
      });
    
    res.status(200).json({
      success: true,
      count: comments.length,
      data: comments
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Add comment to article
// @route   POST /api/articles/:articleId/comments
// @access  Private
exports.addComment = async (req, res, next) => {
  try {
    // Check article exists
    const article = await Article.findById(req.params.articleId);
    
    if (!article) {
      return next(new ErrorResponse(`No article found with id ${req.params.articleId}`, 404));
    }
    
    // Add article id and user id to request body
    req.body.article = req.params.articleId;
    req.body.author = req.user.id;
    
    // Create comment
    const comment = await Comment.create(req.body);
    
    // Get populated comment
    const populatedComment = await Comment.findById(comment._id).populate({
      path: 'author',
      select: 'username'
    });
    
    res.status(201).json({
      success: true,
      data: populatedComment
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete comment
// @route   DELETE /api/articles/:articleId/comments/:id
// @access  Private (Owner or Admin)
exports.deleteComment = async (req, res, next) => {
  try {
    const comment = await Comment.findById(req.params.id);
    
    if (!comment) {
      return next(new ErrorResponse(`No comment found with id ${req.params.id}`, 404));
    }
    
    // Check if user is comment owner or admin
    if (comment.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to delete this comment', 403));
    }
    
    await comment.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
}; 