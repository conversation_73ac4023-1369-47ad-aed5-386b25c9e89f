const Article = require('../models/Article');
const Comment = require('../models/Comment');
const User = require('../models/User');
const Category = require('../models/Category');
const { ErrorResponse } = require('../middleware/errorHandler');

/**
 * @desc    获取博客统计数据
 * @route   GET /api/analytics/stats
 * @access  Private (Admin)
 */
exports.getStats = async (req, res, next) => {
  try {
    // 使用MongoDB聚合框架并行获取统计数据
    const [articleStats, userStats, commentStats, categoryStats] = await Promise.all([
      Article.aggregate([
        {
          $facet: {
            'total': [{ $count: 'count' }],
            'published': [
              { $match: { isPublished: true } },
              { $count: 'count' }
            ],
            'byCategory': [
              { $match: { isPublished: true } },
              { $group: { _id: '$category', count: { $sum: 1 } } },
              { $sort: { count: -1 } },
              { $limit: 5 }
            ],
            'views': [
              { $group: { _id: null, total: { $sum: '$viewCount' } } }
            ]
          }
        }
      ]),
      User.aggregate([
        {
          $facet: {
            'total': [{ $count: 'count' }],
            'byRole': [
              { $group: { _id: '$role', count: { $sum: 1 } } }
            ]
          }
        }
      ]),
      Comment.aggregate([
        {
          $facet: {
            'total': [{ $count: 'count' }],
            'topArticles': [
              { $group: { _id: '$article', count: { $sum: 1 } } },
              { $sort: { count: -1 } },
              { $limit: 5 }
            ]
          }
        }
      ]),
      Category.countDocuments()
    ]);

    // 为了兼容测试，使用原有的响应格式
    res.status(200).json({
      articleCount: articleStats[0].total[0]?.count || 0,
      commentCount: commentStats[0].total[0]?.count || 0,
      userCount: userStats[0].total[0]?.count || 0,
      categoryCount: categoryStats,
      // 添加详细统计数据
      stats: {
        articles: {
          total: articleStats[0].total[0]?.count || 0,
          published: articleStats[0].published[0]?.count || 0,
          totalViews: articleStats[0].views[0]?.total || 0,
          byCategory: articleStats[0].byCategory || []
        },
        users: {
          total: userStats[0].total[0]?.count || 0,
          byRole: userStats[0].byRole || []
        },
        comments: {
          total: commentStats[0].total[0]?.count || 0,
          topArticles: commentStats[0].topArticles || []
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @desc    获取热门文章
 * @route   GET /api/analytics/popular
 * @access  Public
 */
exports.getPopularPosts = async (req, res, next) => {
  try {
    const { limit = 5, days = 30 } = req.query;
    
    // 计算日期范围
    const dateLimit = new Date();
    dateLimit.setDate(dateLimit.getDate() - parseInt(days));
    
    // 使用聚合管道查询热门文章
    const popularPosts = await Article.aggregate([
      { 
        $match: { 
          isPublished: true,
          createdAt: { $gte: dateLimit }
        } 
      },
      {
        $addFields: {
          // 计算文章得分 (结合浏览量和评论数)
          score: { 
            $add: [
              '$viewCount', 
              { $multiply: [{ $size: { $ifNull: ['$comments', []] } }, 5] }
            ] 
          }
        }
      },
      { $sort: { score: -1 } },
      { $limit: parseInt(limit) },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorInfo'
        }
      },
      {
        $project: {
          _id: 1,
          title: 1,
          viewCount: 1,
          createdAt: 1,
          status: 1,
          score: 1,
          category: { $arrayElemAt: ['$categoryInfo.name', 0] },
          author: { $arrayElemAt: ['$authorInfo.username', 0] }
        }
      }
    ]);
    
    // 为了兼容测试，直接返回数组
    res.status(200).json(popularPosts);
  } catch (err) {
    next(err);
  }
};

/**
 * @desc    获取标签云数据
 * @route   GET /api/analytics/tags
 * @access  Public
 */
exports.getTagCloud = async (req, res, next) => {
  try {
    // 使用聚合管道统计标签使用频率
    const tags = await Article.aggregate([
      { $match: { isPublished: true } },
      { $unwind: '$tags' },
      { 
        $group: { 
          _id: '$tags', 
          count: { $sum: 1 },
          articles: { $push: '$_id' }
        } 
      },
      { $sort: { count: -1 } },
      {
        $project: {
          _id: 0,
          name: '$_id',
          count: 1,
          articleCount: { $size: '$articles' }
        }
      }
    ]);
    
    // 为了兼容测试，直接返回数组
    res.status(200).json(tags);
  } catch (err) {
    next(err);
  }
};

/**
 * @desc    获取时间段内的文章发布趋势
 * @route   GET /api/analytics/trends
 * @access  Public
 */
exports.getPublishingTrends = async (req, res, next) => {
  try {
    const { period = 'month', months = 6 } = req.query;
    
    // 计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));
    
    // 根据时间周期选择分组格式
    let dateFormat;
    let interval;
    
    switch(period) {
      case 'day':
        dateFormat = '%Y-%m-%d';
        interval = { $dayOfMonth: '$createdAt' };
        break;
      case 'week':
        dateFormat = '%Y-W%U';
        interval = { $week: '$createdAt' };
        break;
      case 'month':
      default:
        dateFormat = '%Y-%m';
        interval = { $month: '$createdAt' };
    }
    
    // 使用聚合管道分析发布趋势
    const trends = await Article.aggregate([
      { 
        $match: { 
          createdAt: { $gte: startDate, $lte: endDate } 
        } 
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
            published: '$isPublished'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          published: {
            $sum: {
              $cond: [{ $eq: ['$_id.published', true] }, '$count', 0]
            }
          },
          drafts: {
            $sum: {
              $cond: [{ $eq: ['$_id.published', false] }, '$count', 0]
            }
          },
          total: { $sum: '$count' }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    res.status(200).json({
      success: true,
      data: trends
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @desc    获取用户活跃度分析
 * @route   GET /api/analytics/user-activity
 * @access  Private (Admin)
 */
exports.getUserActivity = async (req, res, next) => {
  try {
    // 使用聚合管道分析用户活动
    const userActivity = await Comment.aggregate([
      {
        $group: {
          _id: '$author',
          commentCount: { $sum: 1 },
          firstComment: { $min: '$createdAt' },
          lastComment: { $max: '$createdAt' },
          articles: { $addToSet: '$article' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $project: {
          _id: 0,
          userId: '$_id',
          username: { $arrayElemAt: ['$userInfo.username', 0] },
          commentCount: 1,
          uniqueArticles: { $size: '$articles' },
          firstComment: 1,
          lastComment: 1,
          daysSinceLastComment: {
            $divide: [
              { $subtract: [new Date(), '$lastComment'] },
              1000 * 60 * 60 * 24
            ]
          }
        }
      },
      { $sort: { commentCount: -1 } },
      { $limit: 20 }
    ]);
    
    res.status(200).json({
      success: true,
      data: userActivity
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @desc    获取文章内容分析
 * @route   GET /api/analytics/content
 * @access  Private (Admin)
 */
exports.getContentAnalysis = async (req, res, next) => {
  try {
    // 使用聚合管道分析文章内容
    const contentAnalysis = await Article.aggregate([
      { $match: { isPublished: true } },
      {
        $project: {
          title: 1,
          contentLength: { $strLenCP: '$content' },
          wordCount: {
            $size: { $split: [{ $ifNull: ['$content', ''] }, ' '] }
          },
          hasContentBlocks: {
            $cond: [
              { $gt: [{ $size: { $ifNull: ['$contentBlocks', []] } }, 0] },
              true,
              false
            ]
          },
          tagCount: { $size: { $ifNull: ['$tags', []] } },
          viewCount: 1,
          createdAt: 1
        }
      },
      {
        $group: {
          _id: null,
          totalArticles: { $sum: 1 },
          avgContentLength: { $avg: '$contentLength' },
          avgWordCount: { $avg: '$wordCount' },
          avgTags: { $avg: '$tagCount' },
          avgViews: { $avg: '$viewCount' },
          articlesWithBlocks: { 
            $sum: { $cond: ['$hasContentBlocks', 1, 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalArticles: 1,
          avgContentLength: { $round: ['$avgContentLength', 2] },
          avgWordCount: { $round: ['$avgWordCount', 2] },
          avgTags: { $round: ['$avgTags', 2] },
          avgViews: { $round: ['$avgViews', 2] },
          articlesWithBlocks: 1,
          percentWithBlocks: {
            $round: [
              { $multiply: [
                { $divide: ['$articlesWithBlocks', '$totalArticles'] },
                100
              ]},
              2
            ]
          }
        }
      }
    ]);
    
    res.status(200).json({
      success: true,
      data: contentAnalysis[0] || {}
    });
  } catch (err) {
    next(err);
  }
}; 