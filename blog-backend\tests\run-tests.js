const { spawn } = require('child_process');
const path = require('path');

// Test scripts in order
const testScripts = [
  'db.test.js',
  'api.test.js'
];

// Function to run a single test
const runTest = (testScript) => {
  return new Promise((resolve, reject) => {
    console.log(`\n=== Running ${testScript} ===\n`);
    
    // For Windows compatibility
    const testPath = path.join(__dirname, testScript);
    const isWin = process.platform === 'win32';
    
    // Run mocha directly in Windows
    const testProcess = isWin
      ? spawn('npx', ['mocha', testPath], { stdio: 'inherit' })
      : spawn('node', [path.join(__dirname, '../node_modules/.bin/mocha'), testPath], {
          stdio: 'inherit'
        });
    
    testProcess.on('close', code => {
      if (code !== 0) {
        reject(new Error(`Test ${testScript} failed with exit code ${code}`));
      } else {
        console.log(`\n=== Test ${testScript} completed successfully ===\n`);
        resolve();
      }
    });
    
    testProcess.on('error', (error) => {
      reject(new Error(`Failed to run test ${testScript}: ${error.message}`));
    });
  });
};

// Run all tests in sequence
const runAllTests = async () => {
  console.log('=== Starting test suite ===');
  
  try {
    for (const testScript of testScripts) {
      await runTest(testScript);
    }
    console.log('\n=== All tests completed successfully ===');
    process.exit(0);
  } catch (error) {
    console.error(`\n=== Test suite failed: ${error.message} ===`);
    process.exit(1);
  }
};

// Run the tests
runAllTests(); 