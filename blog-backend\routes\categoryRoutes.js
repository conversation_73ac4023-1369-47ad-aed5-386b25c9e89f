const express = require('express');
const {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory
} = require('../controllers/categoryController');

const { protect, authorize } = require('../middleware/auth');
const { categoryValidationRules } = require('../middleware/validators');

const router = express.Router();

// Category routes
router
  .route('/')
  .get(getCategories)
  .post(protect, authorize('admin'), categoryValidationRules, createCategory);

router
  .route('/:id')
  .get(getCategory)
  .put(protect, authorize('admin'), categoryValidationRules, updateCategory)
  .delete(protect, authorize('admin'), deleteCategory);

module.exports = router; 