const express = require('express');
const {
  getComments,
  addComment,
  deleteComment
} = require('../controllers/commentController');

const { protect, authorize } = require('../middleware/auth');
const { commentValidationRules } = require('../middleware/validators');

// mergeParams allows access to params from parent router
const router = express.Router({ mergeParams: true });

// Comment routes for specific article
router
  .route('/')
  .get(getComments)
  .post(protect, commentValidationRules, addComment);

// Route for deleting comments (admin only)
router
  .route('/:id')
  .delete(protect, deleteComment);

module.exports = router; 