const request = require('supertest');
const { expect } = require('chai');
const { connectDB, closeDB } = require('./setup');

// Import models
const User = require('../models/User');
const Article = require('../models/Article');
const Category = require('../models/Category');
const Comment = require('../models/Comment');

// Create express app
let server;

// Variables to hold test data
let adminToken, userToken, testArticleId, testCategoryId;
const testAdmin = {
  username: 'testadmin',
  email: '<EMAIL>',
  password: 'Password123!',
  role: 'admin'
};
const testUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'Password123!',
  role: 'user'
};
const testArticle = {
  title: 'Test Article',
  content: 'This is a test article content',
  tags: ['test', 'api'],
  isPublished: true
};
const testCategory = {
  name: 'Test Category',
  description: 'A category used for testing'
};
const testComment = {
  content: 'This is a test comment'
};

describe('Blog API Tests', function() {
  // Increase timeout for tests
  this.timeout(10000);

  // Setup before running tests
  before(async function() {
    try {
      await connectDB();
      
      // Load express app after DB connection is established
      server = require('../server');
      
      // Clear test data
      await User.deleteMany({});
      await Article.deleteMany({});
      await Category.deleteMany({});
      await Comment.deleteMany({});
    } catch (err) {
      console.error('Test setup failed:', err);
      process.exit(1);
    }
  });

  // Clean up after tests
  after(async function() {
    await closeDB();
  });

  // Tests for Auth Routes
  describe('Authentication API', () => {
    it('should register a new admin user', async () => {
      const res = await request(server)
        .post('/api/auth/register')
        .send(testAdmin);
      
      expect(res.status).to.equal(201);
      expect(res.body).to.have.property('token');
      expect(res.body).to.have.property('user');
      expect(res.body.user.username).to.equal(testAdmin.username);
      expect(res.body.user.role).to.equal('admin');
      adminToken = res.body.token;
    });
    
    it('should register a new regular user', async () => {
      const res = await request(server)
        .post('/api/auth/register')
        .send(testUser);
      
      expect(res.status).to.equal(201);
      expect(res.body).to.have.property('token');
      expect(res.body).to.have.property('user');
      expect(res.body.user.username).to.equal(testUser.username);
      expect(res.body.user.role).to.equal('user');
      userToken = res.body.token;
    });
    
    it('should login an existing user', async () => {
      const res = await request(server)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('token');
      expect(res.body).to.have.property('user');
      expect(res.body.user.email).to.equal(testUser.email);
    });
    
    it('should not login with invalid credentials', async () => {
      const res = await request(server)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        });
      
      expect(res.status).to.equal(401);
      expect(res.body).to.have.property('error');
    });
  });

  // Tests for Category Routes
  describe('Category API', () => {
    it('should create a new category (admin only)', async () => {
      const res = await request(server)
        .post('/api/categories')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(testCategory);
      
      expect(res.status).to.equal(201);
      expect(res.body).to.have.property('data');
      expect(res.body.data.name).to.equal(testCategory.name);
      testCategoryId = res.body.data._id;
    });
    
    it('should not allow regular users to create categories', async () => {
      const res = await request(server)
        .post('/api/categories')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'Unauthorized Category',
          description: 'Should not be created'
        });
      
      expect(res.status).to.equal(403);
    });
    
    it('should get all categories', async () => {
      const res = await request(server)
        .get('/api/categories');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data).to.be.an('array');
      expect(res.body.data.length).to.be.at.least(1);
    });
  });

  // Tests for Article Routes
  describe('Article API', () => {
    it('should create a new article', async () => {
      const articleWithCategory = {
        ...testArticle,
        category: testCategoryId
      };
      
      const res = await request(server)
        .post('/api/articles')
        .set('Authorization', `Bearer ${userToken}`)
        .send(articleWithCategory);
      
      expect(res.status).to.equal(201);
      expect(res.body).to.have.property('data');
      expect(res.body.data.title).to.equal(testArticle.title);
      testArticleId = res.body.data._id;
    });
    
    it('should get all articles', async () => {
      const res = await request(server)
        .get('/api/articles');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data).to.be.an('array');
      expect(res.body.data.length).to.be.at.least(1);
    });
    
    it('should get a single article by id', async () => {
      const res = await request(server)
        .get(`/api/articles/${testArticleId}`);
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data._id).to.equal(testArticleId);
    });
    
    it('should update an article (owner or admin only)', async () => {
      const updatedArticle = {
        title: 'Updated Test Article',
        content: 'This content has been updated'
      };
      
      const res = await request(server)
        .put(`/api/articles/${testArticleId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updatedArticle);
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data.title).to.equal(updatedArticle.title);
      expect(res.body.data.content).to.equal(updatedArticle.content);
    });
    
    it('should filter articles by category', async () => {
      const res = await request(server)
        .get(`/api/articles?category=${testCategoryId}`);
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data).to.be.an('array');
      expect(res.body.data.length).to.be.at.least(1);
      expect(res.body.data[0].category._id).to.equal(testCategoryId);
    });
    
    it('should filter articles by tag', async () => {
      const res = await request(server)
        .get('/api/articles?tags=test');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data).to.be.an('array');
      expect(res.body.data.length).to.be.at.least(1);
      expect(res.body.data[0].tags).to.include('test');
    });
    
    it('should support pagination of articles', async () => {
      const res = await request(server)
        .get('/api/articles?page=1&limit=10');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body).to.have.property('pagination');
      expect(res.body.pagination).to.have.property('current');
      expect(res.body.pagination.current).to.equal(1);
    });
  });

  // Tests for Comment Routes
  describe('Comment API', () => {
    it('should add a comment to an article', async () => {
      const res = await request(server)
        .post(`/api/articles/${testArticleId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(testComment);
      
      expect(res.status).to.equal(201);
      expect(res.body).to.have.property('data');
      expect(res.body.data.content).to.equal(testComment.content);
    });
    
    it('should get all comments for an article', async () => {
      const res = await request(server)
        .get(`/api/articles/${testArticleId}/comments`);
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('data');
      expect(res.body.data).to.be.an('array');
      expect(res.body.data.length).to.be.at.least(1);
    });
  });

  // Tests for Analytics Routes
  describe('Analytics API', () => {
    it('should get blog statistics (admin only)', async () => {
      const res = await request(server)
        .get('/api/analytics/stats')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).to.equal(200);
      expect(res.body).to.have.property('articleCount');
      expect(res.body).to.have.property('commentCount');
      expect(res.body).to.have.property('userCount');
      expect(res.body).to.have.property('categoryCount');
    });
    
    it('should get popular posts', async () => {
      const res = await request(server)
        .get('/api/analytics/popular');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.be.an('array');
    });
    
    it('should get tag cloud data', async () => {
      const res = await request(server)
        .get('/api/analytics/tags');
      
      expect(res.status).to.equal(200);
      expect(res.body).to.be.an('array');
    });
  });
}); 