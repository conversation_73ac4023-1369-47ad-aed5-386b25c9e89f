// Jest 测试设置文件
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// 在所有测试开始前运行
beforeAll(async () => {
  // 创建内存数据库实例，使用更快的配置
  mongoServer = await MongoMemoryServer.create({
    instance: {
      dbName: 'test',
      storageEngine: 'wiredTiger'
    },
    binary: {
      version: '6.0.0', // 使用较新但稳定的版本
      skipMD5: true
    }
  });
  const mongoUri = mongoServer.getUri();

  // 连接到内存数据库
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  });
}, 60000); // 60秒超时

// 在每个测试后清理数据库
afterEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// 在所有测试结束后运行
afterAll(async () => {
  // 关闭数据库连接
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  // 停止内存数据库
  if (mongoServer) {
    await mongoServer.stop();
  }
});

// 设置测试超时 (60秒)
jest.setTimeout(60000);
