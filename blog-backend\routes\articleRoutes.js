const express = require('express');
const {
  getArticles,
  getAllArticles,
  getArticle,
  createArticle,
  updateArticle,
  deleteArticle
} = require('../controllers/articleController');

const { protect, authorize } = require('../middleware/auth');
const { articleValidationRules } = require('../middleware/validators');

// Include comment router
const commentRouter = require('./commentRoutes');

const router = express.Router();

// Re-route into other resource routers
router.use('/:articleId/comments', commentRouter);

// Article routes
router
  .route('/')
  .get(getArticles)
  .post(protect, articleValidationRules, createArticle);

router.route('/all').get(protect, authorize('admin'), getAllArticles);

router
  .route('/:id')
  .get(getArticle)
  .put(protect, articleValidationRules, updateArticle)
  .delete(protect, authorize('admin'), deleteArticle);

module.exports = router; 