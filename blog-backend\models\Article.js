const mongoose = require('mongoose');

// 内容块模式 - 支持结构化内容
const ContentBlockSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['text', 'heading', 'image', 'code', 'quote', 'list']
  },
  content: {
    type: String,
    required: true
  },
  metadata: {
    language: String,  // 用于代码块
    caption: String,   // 用于图片
    level: Number,     // 用于标题 (1-6)
    listType: String   // 用于列表
  }
}, { _id: false });

const ArticleSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Please provide a title'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters']
    },
    // 结构化内容块 - 可选使用
    contentBlocks: {
      type: [ContentBlockSchema],
      default: []
    },
    // 保留原始内容字段以兼容
    content: {
      type: String,
      required: function() {
        return !this.contentBlocks || this.contentBlocks.length === 0;
      },
      minlength: [10, 'Content should be at least 10 characters long']
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    tags: {
      type: [String],
      default: []
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: false
    },
    viewCount: {
      type: Number,
      default: 0
    },
    // 扩展状态选项
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft'
    },
    isPublished: {
      type: Boolean,
      default: false
    },
    // 版本历史 - 使用MongoDB文档结构存储历史版本
    versions: [{
      title: String,
      content: String,
      contentBlocks: [ContentBlockSchema],
      savedAt: {
        type: Date,
        default: Date.now
      }
    }],
    // 动态元数据 - 利用MongoDB的灵活文档结构
    metadata: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// 虚拟字段 - 评论
ArticleSchema.virtual('comments', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'article',
  justOne: false
});

// 创建索引以优化查询性能
ArticleSchema.index({ title: 'text', content: 'text' }); // 全文搜索
ArticleSchema.index({ tags: 1 }); // 按标签查询
ArticleSchema.index({ category: 1 }); // 按分类查询
ArticleSchema.index({ status: 1, createdAt: -1 }); // 按状态和日期过滤
ArticleSchema.index({ author: 1, createdAt: -1 }); // 按作者查询
ArticleSchema.index({ viewCount: -1 }); // 热门文章

// 中间件 - 保存前处理
ArticleSchema.pre('save', function(next) {
  // 状态和isPublished同步
  if (this.isNew || this.isModified('status')) {
    this.isPublished = this.status === 'published';
  } else if (this.isModified('isPublished')) {
    this.status = this.isPublished ? 'published' : 'draft';
  }
  
  // 保存版本历史
  if (this.isModified('content') || this.isModified('title') || 
      this.isModified('contentBlocks')) {
    
    // 创建版本记录
    const version = {
      title: this.title,
      content: this.content,
      contentBlocks: this.contentBlocks,
      savedAt: new Date()
    };
    
    // 添加到版本历史，限制最多保存10个版本
    this.versions = this.versions || [];
    this.versions.unshift(version);
    
    if (this.versions.length > 10) {
      this.versions = this.versions.slice(0, 10);
    }
  }
  
  next();
});

module.exports = mongoose.model('Article', ArticleSchema); 