const mongoose = require('mongoose');

const CommentSchema = new mongoose.Schema(
  {
    content: {
      type: String,
      required: [true, 'Please provide comment content'],
      trim: true,
      maxlength: [1000, 'Comment cannot be more than 1000 characters']
    },
    article: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Article',
      required: true
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    parentComment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment',
      default: null
    },
    replies: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }],
    likes: {
      type: Number,
      default: 0
    },
    likedBy: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'spam'],
      default: 'approved'
    }
  },
  { timestamps: true }
);

CommentSchema.index({ article: 1, createdAt: -1 });
CommentSchema.index({ parentComment: 1 });
CommentSchema.index({ author: 1 });
CommentSchema.index({ likes: -1 });

CommentSchema.post('save', async function(doc) {
  if (doc.parentComment) {
    await mongoose.model('Comment').findByIdAndUpdate(
      doc.parentComment,
      { $addToSet: { replies: doc._id } }
    );
  }
});

CommentSchema.statics.getCommentTree = async function(articleId) {
  const rootComments = await this.find({ 
    article: articleId,
    parentComment: null 
  }).sort({ createdAt: -1 });
  
  for (let comment of rootComments) {
    await comment.populate({
      path: 'replies',
      options: { sort: { createdAt: 1 } },
      populate: {
        path: 'author',
        select: 'username'
      }
    }).execPopulate();
  }
  
  return rootComments;
};

CommentSchema.methods.like = async function(userId) {
  if (this.likedBy.includes(userId)) {
    return this;
  }
  
  this.likedBy.push(userId);
  this.likes = this.likedBy.length;
  return this.save();
};

CommentSchema.methods.unlike = async function(userId) {
  this.likedBy = this.likedBy.filter(id => id.toString() !== userId.toString());
  this.likes = this.likedBy.length;
  return this.save();
};

module.exports = mongoose.model('Comment', CommentSchema); 