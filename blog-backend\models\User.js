const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const UserSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      required: [true, 'Please provide a username'],
      unique: true,
      trim: true,
      maxlength: [50, 'Username cannot be more than 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Please provide an email'],
      unique: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email'
      ]
    },
    password: {
      type: String,
      required: [true, 'Please provide a password'],
      minlength: 6,
      select: false
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user'
    },
    // 个人资料信息
    profile: {
      name: String,
      bio: String,
      location: String,
      website: String,
      avatar: String,
      social: {
        twitter: String,
        facebook: String,
        linkedin: String,
        github: String
      }
    },
    // 活动跟踪
    activity: {
      lastActive: {
        type: Date,
        default: Date.now
      },
      lastLogin: Date,
      loginCount: {
        type: Number,
        default: 0
      }
    },
    // 用户偏好设置
    preferences: {
      emailNotifications: {
        type: Boolean,
        default: true
      },
      theme: {
        type: String,
        enum: ['light', 'dark', 'system'],
        default: 'system'
      },
      articlesPerPage: {
        type: Number,
        default: 10
      }
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// 虚拟字段 - 用户的文章
UserSchema.virtual('articles', {
  ref: 'Article',
  localField: '_id',
  foreignField: 'author',
  justOne: false
});

// 虚拟字段 - 用户的评论
UserSchema.virtual('comments', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'author',
  justOne: false
});

// 索引优化
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ 'activity.lastActive': -1 });

// Encrypt password using bcrypt
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign(
    { id: this._id, username: this.username, role: this.role },
    process.env.JWT_SECRET || 'testsecret',
    {
      expiresIn: process.env.JWT_EXPIRE || '30d'
    }
  );
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// 更新用户最后活动时间
UserSchema.methods.updateActivity = async function() {
  this.activity.lastActive = Date.now();
  return this.save({ validateBeforeSave: false });
};

// 记录用户登录
UserSchema.methods.recordLogin = async function() {
  this.activity.lastLogin = Date.now();
  this.activity.loginCount += 1;
  return this.save({ validateBeforeSave: false });
};

module.exports = mongoose.model('User', UserSchema); 