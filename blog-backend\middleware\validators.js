const { body } = require('express-validator');
const { validationResult } = require('express-validator');
const { ErrorResponse } = require('./errorHandler');
const mongoose = require('mongoose');

// Middleware to check validation results
exports.validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      success: false,
      errors: errors.array().map(err => ({
        field: err.path,
        message: err.msg
      })) 
    });
  }
  next();
};

// Validate ObjectId
const validateObjectId = (value) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    throw new Error('Invalid ID format');
  }
  return true;
};

// User validation rules
exports.userValidationRules = [
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters'),

  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please include a valid email'),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
    
  (req, res, next) => exports.validate(req, res, next)
];

// Login validation rules
exports.loginValidationRules = [
  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please include a valid email'),

  body('password')
    .notEmpty()
    .withMessage('Password is required'),
    
  (req, res, next) => exports.validate(req, res, next)
];

// Article validation rules
exports.articleValidationRules = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 100 })
    .withMessage('Title cannot be more than 100 characters'),

  body('content')
    .notEmpty()
    .withMessage('Content is required')
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters'),
    
  (req, res, next) => exports.validate(req, res, next)
];

// Category validation rules
exports.categoryValidationRules = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ max: 50 })
    .withMessage('Category name cannot be more than 50 characters'),
    
  (req, res, next) => exports.validate(req, res, next)
];

// Comment validation rules
exports.commentValidationRules = [
  body('content')
    .notEmpty()
    .withMessage('Comment content is required')
    .isLength({ max: 1000 })
    .withMessage('Comment cannot be more than 1000 characters'),
    
  (req, res, next) => exports.validate(req, res, next)
]; 