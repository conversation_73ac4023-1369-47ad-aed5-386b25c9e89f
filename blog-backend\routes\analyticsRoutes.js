const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getStats,
  getPopularPosts,
  getTagCloud,
  getPublishingTrends,
  getUserActivity,
  getContentAnalysis
} = require('../controllers/analyticsController');

// 统计数据路由 - 仅管理员可访问
router.get('/stats', protect, authorize('admin'), getStats);

// 公开分析路由
router.get('/popular', getPopularPosts);
router.get('/tags', getTagCloud);
router.get('/trends', getPublishingTrends);

// 管理员专用分析路由
router.get('/user-activity', protect, authorize('admin'), getUserActivity);
router.get('/content', protect, authorize('admin'), getContentAnalysis);

module.exports = router; 