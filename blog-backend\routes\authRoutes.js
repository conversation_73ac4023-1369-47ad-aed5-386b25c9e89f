const express = require('express');
const router = express.Router();
const { 
  register, 
  login, 
  getMe,
  updateProfile,
  updatePassword,
  forgotPassword
} = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { userValidationRules, loginValidationRules } = require('../middleware/validators');

// Register route
router.post('/register', userValidationRules, register);

// Login route
router.post('/login', loginValidationRules, login);

// Get current user route
router.get('/me', protect, getMe);

// Update profile route
router.put('/profile', protect, updateProfile);

// Update password route
router.put('/password', protect, updatePassword);

// Forgot password route
router.post('/forgot-password', forgotPassword);

module.exports = router; 